/**
 * 外卖模块API接口
 *
 * 该文件实现了外卖系统的API接口，包括商家、外卖商品、购物车、订单等功能。
 * 与后端路由保持一致，提供完整的外卖服务。
 */

import { http } from '@/utils/http'
import type {
  Merchant,
  TakeoutCategory,
  TakeoutFood,
  TakeoutCartSummary,
  TakeoutCartItem,
  TakeoutOrder,
  MerchantQueryRequest,
  TakeoutFoodListQuery,
  AddTakeoutToCartRequest,
  UpdateTakeoutCartItemRequest,
  SelectCartItemRequest,
  CreateTakeoutOrderRequest,
  OrderListQuery,
  MerchantResponseList,
  TakeoutFoodListResponse,
  TakeoutCategoryResponseList,
  TakeoutOrderListResponse,
} from './takeout.typings'

// ==================== 商家相关 ====================

/**
 * 获取商家列表
 */
export const getMerchantList = async (
  params: MerchantQueryRequest,
): Promise<MerchantResponseList> => {
  const response = await http.get<MerchantResponseList>('/api/v1/user/takeout/merchants', params)
  return response.data
}

/**
 * 获取商家详情
 */
export const getMerchantDetail = async (id: number): Promise<Merchant> => {
  const response = await http.get<Merchant>(`/api/v1/user/takeout/merchants/${id}`)
  return response.data
}

/**
 * 获取推荐商家列表
 */
export const getRecommendedMerchants = async (params?: {
  page?: number
  pageSize?: number
}): Promise<MerchantResponseList> => {
  const response = await http.get<MerchantResponseList>('/api/v1/merchant/recommended-merchants', {
    params,
  })
  return response.data
}

// ==================== 分类相关 ====================

/**
 * 获取全局分类列表
 */
export const getGlobalCategories = async (): Promise<TakeoutCategoryResponseList> => {
  const response = await http.get<TakeoutCategoryResponseList>('/api/takeout/categories/global')
  return response.data
}

/**
 * 获取商家分类列表
 */
export const getMerchantCategories = async (
  merchantId: number,
): Promise<TakeoutCategoryResponseList> => {
  const response = await http.get<TakeoutCategoryResponseList>(
    `/api/v1/user/takeout/merchants/${merchantId}/categories`,
  )
  return response.data
}

// ==================== 外卖商品相关 ====================

/**
 * 获取外卖商品列表
 */
export const getTakeoutFoodList = async (
  params: TakeoutFoodListQuery,
): Promise<TakeoutFoodListResponse> => {
  const response = await http.get<TakeoutFoodListResponse>('/api/takeout/foods', params)
  return response.data
}

/**
 * 获取商家商品列表
 */
export const getMerchantFoodList = async (
  merchantId: number,
  params?: {
    page?: number
    pageSize?: number
    category_id?: number
  },
): Promise<TakeoutFoodListResponse> => {
  const response = await http.get<TakeoutFoodListResponse>(
    `/api/v1/user/takeout/merchants/${merchantId}/foods`,
    params,
  )
  return response.data
}

/**
 * 获取外卖商品详情
 */
export const getTakeoutFoodDetail = async (id: number): Promise<TakeoutFood> => {
  const response = await http.get<TakeoutFood>(`/api/v1/user/takeout/foods/${id}`)
  return response.data
}

// ==================== 购物车相关 ====================

/**
 * 添加商品到购物车
 */
export const addToTakeoutCart = async (
  params: AddTakeoutToCartRequest,
): Promise<TakeoutCartItem> => {
  const response = await http.post<TakeoutCartItem>('/api/v1/user/takeout/cart/add', params)
  return response.data
}

/**
 * 更新购物车商品
 */
export const updateTakeoutCartItem = async (
  params: UpdateTakeoutCartItemRequest,
): Promise<TakeoutCartItem> => {
  const response = await http.post<TakeoutCartItem>('/api/v1/user/takeout/cart/update', params)
  return response.data
}

/**
 * 删除购物车商品
 */
export const removeTakeoutCartItem = async (cartItemId: number): Promise<void> => {
  await http.delete(`/api/v1/user/takeout/cart/${cartItemId}`)
}

/**
 * 获取购物车列表
 */
export const getTakeoutCart = async (forceRefresh = false): Promise<TakeoutCartSummary> => {
  // 添加时间戳参数避免缓存
  const params = forceRefresh ? { _t: Date.now() } : {}
  const response = await http.get<TakeoutCartItem[]>('/api/v1/user/takeout/cart/list', params)
  const items = response.data || []

  console.log('🛒 [API] 获取购物车列表响应:', {
    itemsCount: items.length,
    forceRefresh,
    timestamp: Date.now(),
  })

  // 计算统计信息
  const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0)
  const totalAmount = items.reduce((sum, item) => sum + item.subtotal, 0)
  const originalAmount = items.reduce((sum, item) => sum + item.original_price * item.quantity, 0)
  const totalPackagingFee = items.reduce((sum, item) => sum + item.packaging_fee * item.quantity, 0)

  // 获取商家信息（从第一个商品获取，假设购物车中只有一个商家的商品）
  const firstItem = items[0]

  return {
    items,
    total_items: items.length,
    total_quantity: totalQuantity,
    total_amount: totalAmount,
    original_amount: originalAmount,
    total_discount: originalAmount - totalAmount,
    total_packaging_fee: totalPackagingFee,
    delivery_fee: 0, // 配送费需要单独计算
    merchant_id: firstItem?.merchant_id || 0,
    merchant_name: firstItem?.merchant_name || '',
    merchant_logo: '', // 商家logo需要从商家信息获取
    merchant_min_order_amount: 0, // 起送金额需要从商家信息获取
  }
}

/**
 * 获取购物车数量详情
 */
export const getTakeoutCartCount = (): Promise<{
  total_items: number
  total_quantity: number
  selected_items: number
  selected_quantity: number
  unselected_items: number
  unselected_quantity: number
  total_amount: number
  selected_amount: number
  merchant_count: number
}> => {
  return http.get('/api/v1/user/takeout/cart/count-details').then((res) => res.data) as Promise<{
    total_items: number
    total_quantity: number
    selected_items: number
    selected_quantity: number
    unselected_items: number
    unselected_quantity: number
    total_amount: number
    selected_amount: number
    merchant_count: number
  }>
}

/**
 * 选择购物车商品
 */
export const selectTakeoutCartItems = async (params: SelectCartItemRequest): Promise<void> => {
  await http.post('/api/v1/user/takeout/cart/select', params)
}

/**
 * 清空购物车
 */
export const clearTakeoutCart = async (): Promise<void> => {
  await http.delete('/api/v1/user/takeout/cart/clear')
}

// ==================== 订单相关 ====================

/**
 * 创建订单
 */
export const createTakeoutOrder = async (
  params: CreateTakeoutOrderRequest,
): Promise<TakeoutOrder> => {
  const response = await http.post<TakeoutOrder>('/api/v1/user/takeout/order/create', params)
  return response.data
}

/**
 * 获取订单列表
 */
export const getTakeoutOrderList = async (
  params?: OrderListQuery,
): Promise<TakeoutOrderListResponse> => {
  const response = await http.get<TakeoutOrderListResponse>('/api/v1/user/takeout/order', params)
  return response.data
}

/**
 * 获取订单详情
 */
export const getTakeoutOrderDetail = async (orderId: number): Promise<TakeoutOrder> => {
  const response = await http.get<TakeoutOrder>(`/api/v1/user/takeout/order/${orderId}`)
  return response.data
}

/**
 * 取消订单
 */
export const cancelTakeoutOrder = async (orderId: number, reason?: string): Promise<void> => {
  await http.post(`/api/v1/user/takeout/order/${orderId}/cancel`, { reason })
}

/**
 * 确认收货
 */
export const confirmTakeoutOrder = async (orderId: number): Promise<void> => {
  await http.post(`/api/v1/user/takeout/order/${orderId}/confirm`)
}

/**
 * 订单支付
 */
export const payTakeoutOrder = async (
  orderId: number,
  paymentMethod: string,
): Promise<{
  payment_url?: string
  payment_id: string
}> => {
  const response = await http.post<{
    payment_url?: string
    payment_id: string
  }>(`/api/v1/user/takeout/order/${orderId}/pay`, {
    payment_method: paymentMethod,
  })
  return response.data
}

// ==================== 支付相关 ====================

/**
 * 查询支付状态
 */
export const queryTakeoutPaymentStatus = async (
  orderId: number,
): Promise<{
  payment_status: string
  paid_at?: string
  amount: number
}> => {
  const response = await http.get<{
    payment_status: string
    paid_at?: string
    amount: number
  }>(`/api/takeout/orders/${orderId}/payment/status`)
  return response.data
}

/**
 * 关闭支付
 */
export const closeTakeoutPayment = async (orderId: number): Promise<void> => {
  await http.post(`/api/takeout/orders/${orderId}/payment/close`)
}
